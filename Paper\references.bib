@inproceedings{chen2010automated,
  title={An automated vulnerability scanner for injection attack based on injection point},
  author={<PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>},
  booktitle={2010 international computer symposium (ICS2010)},
  pages={113--118},
  year={2010},
  organization={IEEE}
}

@inproceedings{gregory2007using,
  title={Using the flask security architecture to facilitate risk adaptable access controls},
  author={<PERSON>, <PERSON> and <PERSON>, <PERSON>},
  booktitle={Third Annual Security Enhanced Linux Symposium},
  pages={1--7},
  year={2007}
}

@inproceedings{patil2016design,
  title={Design of efficient web vulnerability scanner},
  author={<PERSON><PERSON>, <PERSON>mit<PERSON> and <PERSON>, Nilesh and Padiya, Puja},
  booktitle={2016 International Conference on Inventive Computation Technologies (ICICT)},
  volume={2},
  pages={1--6},
  year={2016},
  organization={IEEE}
}


@article{sonmez2021holistic,
  title={Holistic web application security visualization for multi-project and multi-phase dynamic application security test results},
  author={S{\"o}nmez, <PERSON><PERSON> {\"O}z<PERSON><PERSON> and <PERSON><PERSON>{\c{c}}, <PERSON><PERSON>{\"u}nel},
  journal={IEEE Access},
  volume={9},
  pages={25858--25884},
  year={2021},
  publisher={IEEE}
}


@inproceedings{wang2019research,
  title={Research on web application security vulnerability scanning technology},
  author={Wang, Bin and Liu, Lu and Li, Feng and Zhang, Jianye and Chen, Tao and Zou, Zhenwan},
  booktitle={2019 IEEE 4th Advanced Information Technology, Electronic and Automation Control Conference (IAEAC)},
  pages={1524--1528},
  year={2019},
  organization={IEEE}
}

@article{khalid2020web,
  title={Web vulnerability finder (WVF): automated black-box web vulnerability scanner},
  author={Khalid, Muhammad Noman and Iqbal, Muhammad and Rasheed, Kamran and Abid, Malik Muneeb},
  journal={Int J Inf Technol Comput Sci},
  volume={12},
  number={4},
  pages={38--46},
  year={2020}
}

@inproceedings{bau2010state,
  title={State of the art: Automated black-box web application vulnerability testing},
  author={Bau, Jason and Bursztein, Elie and Gupta, Divij and Mitchell, John},
  booktitle={2010 IEEE symposium on security and privacy},
  pages={332--345},
  year={2010},
  organization={IEEE}
}

@inproceedings{fonseca2007testing,
  title={Testing and comparing web vulnerability scanning tools for SQL injection and XSS attacks},
  author={Fonseca, Jose and Vieira, Marco and Madeira, Henrique},
  booktitle={13th Pacific Rim international symposium on dependable computing (PRDC 2007)},
  pages={365--372},
  year={2007},
  organization={IEEE}
}

@article{moshika2021vulnerability,
  title={Vulnerability assessment in heterogeneous web environment using probabilistic arithmetic automata},
  author={Moshika, A and Thirumaran, M and Natarajan, Balaji and Andal, K and Sambasivam, G and Manoharan, Rajesh},
  journal={IEEE Access},
  volume={9},
  pages={74659--74673},
  year={2021},
  publisher={IEEE}
}

@article{huang2005testing,
  title={A testing framework for web application security assessment},
  author={Huang, Yao-Wen and Tsai, Chung-Hung and Lin, Tsung-Po and Huang, Shih-Kun and Lee, DT and Kuo, Sy-Yen},
  journal={Computer Networks},
  volume={48},
  number={5},
  pages={739--761},
  year={2005},
  publisher={Elsevier}
}


@inproceedings{mayan2014test,
  title={Test case optimization using hybrid search technique},
  author={Mayan, J Albert and Ravi, T},
  booktitle={Proceedings of the 2014 International Conference on Interdisciplinary Advances in Applied Computing},
  pages={1--7},
  year={2014}
}



@inproceedings{lam2008securing,
  title={Securing web applications with static and dynamic information flow tracking},
  author={Lam, Monica S and Martin, Michael and Livshits, Benjamin and Whaley, John},
  booktitle={Proceedings of the 2008 ACM SIGPLAN symposium on Partial evaluation and semantics-based program manipulation},
  pages={3--12},
  year={2008}
}

@article{suto2010analyzing,
  title={Analyzing the accuracy and time costs of web application security scanners},
  author={Suto, Larry},
  journal={San Francisco, February},
  volume={1},
  year={2010}
}

@article{paquiao2025role,
  title={The Role of Hashing Libraries in Flask Application Security: A Focus on Password Protection},
  author={Paquiao, Lord Ian M and Bajao, Zoren E},
  journal={International Journal},
  volume={14},
  number={7},
  year={2025}
}

@inproceedings{mcallister2008leveraging,
  title={Leveraging user interactions for in-depth testing of web applications},
  author={McAllister, Sean and Kirda, Engin and Kruegel, Christopher},
  booktitle={International Workshop on Recent Advances in Intrusion Detection},
  pages={191--210},
  year={2008},
  organization={Springer}
}

@inproceedings{fowler2018fuzz,
  title={Fuzz testing for automotive cyber-security},
  author={Fowler, Daniel S and Bryans, Jeremy and Shaikh, Siraj Ahmed and Wooderson, Paul},
  booktitle={2018 48th Annual IEEE/IFIP International Conference on Dependable Systems and Networks Workshops (DSN-W)},
  pages={239--246},
  year={2018},
  organization={IEEE}
}
