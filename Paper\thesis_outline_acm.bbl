%%% -*-BibTeX-*-
%%% Do NOT edit. File created by BibTeX with style
%%% ACM-Reference-Format-Journals [18-Jan-2012].

\begin{thebibliography}{16}

%%% ====================================================================
%%% NOTE TO THE USER: you can override these defaults by providing
%%% customized versions of any of these macros before the \bibliography
%%% command.  Each of them MUST provide its own final punctuation,
%%% except for \shownote{} and \showURL{}.  The latter two
%%% do not use final punctuation, in order to avoid confusing it with
%%% the Web address.
%%%
%%% To suppress output of a particular field, define its macro to expand
%%% to an empty string, or better, \unskip, like this:
%%%
%%% \newcommand{\showURL}[1]{\unskip}   % LaTeX syntax
%%%
%%% \def \showURL #1{\unskip}           % plain TeX syntax
%%%
%%% ====================================================================

\ifx \showCODEN    \undefined \def \showCODEN     #1{\unskip}     \fi
\ifx \showISBNx    \undefined \def \showISBNx     #1{\unskip}     \fi
\ifx \showISBNxiii \undefined \def \showISBNxiii  #1{\unskip}     \fi
\ifx \showISSN     \undefined \def \showISSN      #1{\unskip}     \fi
\ifx \showLCCN     \undefined \def \showLCCN      #1{\unskip}     \fi
\ifx \shownote     \undefined \def \shownote      #1{#1}          \fi
\ifx \showarticletitle \undefined \def \showarticletitle #1{#1}   \fi
\ifx \showURL      \undefined \def \showURL       {\relax}        \fi
% The following commands are used for tagged output and should be
% invisible to TeX
\providecommand\bibfield[2]{#2}
\providecommand\bibinfo[2]{#2}
\providecommand\natexlab[1]{#1}
\providecommand\showeprint[2][]{arXiv:#2}

\bibitem[Bau et~al\mbox{.}(2010)]%
        {bau2010state}
\bibfield{author}{\bibinfo{person}{Jason Bau}, \bibinfo{person}{Elie Bursztein}, \bibinfo{person}{Divij Gupta}, {and} \bibinfo{person}{John Mitchell}.} \bibinfo{year}{2010}\natexlab{}.
\newblock \showarticletitle{State of the art: Automated black-box web application vulnerability testing}. In \bibinfo{booktitle}{\emph{2010 IEEE symposium on security and privacy}}. IEEE, \bibinfo{pages}{332--345}.
\newblock


\bibitem[Chen and Wu(2010)]%
        {chen2010automated}
\bibfield{author}{\bibinfo{person}{Jan-Min Chen} {and} \bibinfo{person}{Chia-Lun Wu}.} \bibinfo{year}{2010}\natexlab{}.
\newblock \showarticletitle{An automated vulnerability scanner for injection attack based on injection point}. In \bibinfo{booktitle}{\emph{2010 international computer symposium (ICS2010)}}. IEEE, \bibinfo{pages}{113--118}.
\newblock


\bibitem[Fonseca et~al\mbox{.}(2007)]%
        {fonseca2007testing}
\bibfield{author}{\bibinfo{person}{Jose Fonseca}, \bibinfo{person}{Marco Vieira}, {and} \bibinfo{person}{Henrique Madeira}.} \bibinfo{year}{2007}\natexlab{}.
\newblock \showarticletitle{Testing and comparing web vulnerability scanning tools for SQL injection and XSS attacks}. In \bibinfo{booktitle}{\emph{13th Pacific Rim international symposium on dependable computing (PRDC 2007)}}. IEEE, \bibinfo{pages}{365--372}.
\newblock


\bibitem[Fowler et~al\mbox{.}(2018)]%
        {fowler2018fuzz}
\bibfield{author}{\bibinfo{person}{Daniel~S Fowler}, \bibinfo{person}{Jeremy Bryans}, \bibinfo{person}{Siraj~Ahmed Shaikh}, {and} \bibinfo{person}{Paul Wooderson}.} \bibinfo{year}{2018}\natexlab{}.
\newblock \showarticletitle{Fuzz testing for automotive cyber-security}. In \bibinfo{booktitle}{\emph{2018 48th Annual IEEE/IFIP International Conference on Dependable Systems and Networks Workshops (DSN-W)}}. IEEE, \bibinfo{pages}{239--246}.
\newblock


\bibitem[Gregory and Loscocco(2007)]%
        {gregory2007using}
\bibfield{author}{\bibinfo{person}{Machon Gregory} {and} \bibinfo{person}{Peter Loscocco}.} \bibinfo{year}{2007}\natexlab{}.
\newblock \showarticletitle{Using the flask security architecture to facilitate risk adaptable access controls}. In \bibinfo{booktitle}{\emph{Third Annual Security Enhanced Linux Symposium}}. \bibinfo{pages}{1--7}.
\newblock


\bibitem[Huang et~al\mbox{.}(2005)]%
        {huang2005testing}
\bibfield{author}{\bibinfo{person}{Yao-Wen Huang}, \bibinfo{person}{Chung-Hung Tsai}, \bibinfo{person}{Tsung-Po Lin}, \bibinfo{person}{Shih-Kun Huang}, \bibinfo{person}{DT Lee}, {and} \bibinfo{person}{Sy-Yen Kuo}.} \bibinfo{year}{2005}\natexlab{}.
\newblock \showarticletitle{A testing framework for web application security assessment}.
\newblock \bibinfo{journal}{\emph{Computer Networks}} \bibinfo{volume}{48}, \bibinfo{number}{5} (\bibinfo{year}{2005}), \bibinfo{pages}{739--761}.
\newblock


\bibitem[Khalid et~al\mbox{.}(2020)]%
        {khalid2020web}
\bibfield{author}{\bibinfo{person}{Muhammad~Noman Khalid}, \bibinfo{person}{Muhammad Iqbal}, \bibinfo{person}{Kamran Rasheed}, {and} \bibinfo{person}{Malik~Muneeb Abid}.} \bibinfo{year}{2020}\natexlab{}.
\newblock \showarticletitle{Web vulnerability finder (WVF): automated black-box web vulnerability scanner}.
\newblock \bibinfo{journal}{\emph{Int J Inf Technol Comput Sci}} \bibinfo{volume}{12}, \bibinfo{number}{4} (\bibinfo{year}{2020}), \bibinfo{pages}{38--46}.
\newblock


\bibitem[Lam et~al\mbox{.}(2008)]%
        {lam2008securing}
\bibfield{author}{\bibinfo{person}{Monica~S Lam}, \bibinfo{person}{Michael Martin}, \bibinfo{person}{Benjamin Livshits}, {and} \bibinfo{person}{John Whaley}.} \bibinfo{year}{2008}\natexlab{}.
\newblock \showarticletitle{Securing web applications with static and dynamic information flow tracking}. In \bibinfo{booktitle}{\emph{Proceedings of the 2008 ACM SIGPLAN symposium on Partial evaluation and semantics-based program manipulation}}. \bibinfo{pages}{3--12}.
\newblock


\bibitem[Mayan and Ravi(2014)]%
        {mayan2014test}
\bibfield{author}{\bibinfo{person}{J~Albert Mayan} {and} \bibinfo{person}{T Ravi}.} \bibinfo{year}{2014}\natexlab{}.
\newblock \showarticletitle{Test case optimization using hybrid search technique}. In \bibinfo{booktitle}{\emph{Proceedings of the 2014 International Conference on Interdisciplinary Advances in Applied Computing}}. \bibinfo{pages}{1--7}.
\newblock


\bibitem[McAllister et~al\mbox{.}(2008)]%
        {mcallister2008leveraging}
\bibfield{author}{\bibinfo{person}{Sean McAllister}, \bibinfo{person}{Engin Kirda}, {and} \bibinfo{person}{Christopher Kruegel}.} \bibinfo{year}{2008}\natexlab{}.
\newblock \showarticletitle{Leveraging user interactions for in-depth testing of web applications}. In \bibinfo{booktitle}{\emph{International Workshop on Recent Advances in Intrusion Detection}}. Springer, \bibinfo{pages}{191--210}.
\newblock


\bibitem[Moshika et~al\mbox{.}(2021)]%
        {moshika2021vulnerability}
\bibfield{author}{\bibinfo{person}{A Moshika}, \bibinfo{person}{M Thirumaran}, \bibinfo{person}{Balaji Natarajan}, \bibinfo{person}{K Andal}, \bibinfo{person}{G Sambasivam}, {and} \bibinfo{person}{Rajesh Manoharan}.} \bibinfo{year}{2021}\natexlab{}.
\newblock \showarticletitle{Vulnerability assessment in heterogeneous web environment using probabilistic arithmetic automata}.
\newblock \bibinfo{journal}{\emph{IEEE Access}}  \bibinfo{volume}{9} (\bibinfo{year}{2021}), \bibinfo{pages}{74659--74673}.
\newblock


\bibitem[Paquiao and Bajao(2025)]%
        {paquiao2025role}
\bibfield{author}{\bibinfo{person}{Lord Ian~M Paquiao} {and} \bibinfo{person}{Zoren~E Bajao}.} \bibinfo{year}{2025}\natexlab{}.
\newblock \showarticletitle{The Role of Hashing Libraries in Flask Application Security: A Focus on Password Protection}.
\newblock \bibinfo{journal}{\emph{International Journal}} \bibinfo{volume}{14}, \bibinfo{number}{7} (\bibinfo{year}{2025}).
\newblock


\bibitem[Patil et~al\mbox{.}(2016)]%
        {patil2016design}
\bibfield{author}{\bibinfo{person}{Smita Patil}, \bibinfo{person}{Nilesh Marathe}, {and} \bibinfo{person}{Puja Padiya}.} \bibinfo{year}{2016}\natexlab{}.
\newblock \showarticletitle{Design of efficient web vulnerability scanner}. In \bibinfo{booktitle}{\emph{2016 International Conference on Inventive Computation Technologies (ICICT)}}, Vol.~\bibinfo{volume}{2}. IEEE, \bibinfo{pages}{1--6}.
\newblock


\bibitem[S{\"o}nmez and Kili{\c{c}}(2021)]%
        {sonmez2021holistic}
\bibfield{author}{\bibinfo{person}{Ferda~{\"O}zdemir S{\"o}nmez} {and} \bibinfo{person}{Banu~G{\"u}nel Kili{\c{c}}}.} \bibinfo{year}{2021}\natexlab{}.
\newblock \showarticletitle{Holistic web application security visualization for multi-project and multi-phase dynamic application security test results}.
\newblock \bibinfo{journal}{\emph{IEEE Access}}  \bibinfo{volume}{9} (\bibinfo{year}{2021}), \bibinfo{pages}{25858--25884}.
\newblock


\bibitem[Suto(2010)]%
        {suto2010analyzing}
\bibfield{author}{\bibinfo{person}{Larry Suto}.} \bibinfo{year}{2010}\natexlab{}.
\newblock \showarticletitle{Analyzing the accuracy and time costs of web application security scanners}.
\newblock \bibinfo{journal}{\emph{San Francisco, February}}  \bibinfo{volume}{1} (\bibinfo{year}{2010}).
\newblock


\bibitem[Wang et~al\mbox{.}(2019)]%
        {wang2019research}
\bibfield{author}{\bibinfo{person}{Bin Wang}, \bibinfo{person}{Lu Liu}, \bibinfo{person}{Feng Li}, \bibinfo{person}{Jianye Zhang}, \bibinfo{person}{Tao Chen}, {and} \bibinfo{person}{Zhenwan Zou}.} \bibinfo{year}{2019}\natexlab{}.
\newblock \showarticletitle{Research on web application security vulnerability scanning technology}. In \bibinfo{booktitle}{\emph{2019 IEEE 4th Advanced Information Technology, Electronic and Automation Control Conference (IAEAC)}}. IEEE, \bibinfo{pages}{1524--1528}.
\newblock


\end{thebibliography}
