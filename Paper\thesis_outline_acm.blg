This is BibTeX, Version 0.99d (TeX Live 2025)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: thesis_outline_acm.aux
The style file: ACM-Reference-Format.bst
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated wiz_functions (elt_size=4) to 6000 items from 3000.
Database file #1: references.bib
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated glb_str_ptr (elt_size=4) to 20 items from 10.
Reallocated global_strs (elt_size=200001) to 20 items from 10.
Reallocated glb_str_end (elt_size=4) to 20 items from 10.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated wiz_functions (elt_size=4) to 9000 items from 6000.
You've used 4 entries,
            6056 wiz_defined-function locations,
            1506 strings with 18447 characters,
and the built_in function-call counts, 3455 in all, are:
= -- 438
> -- 82
< -- 0
+ -- 22
- -- 30
* -- 204
:= -- 344
add.period$ -- 20
call.type$ -- 4
change.case$ -- 22
chr.to.int$ -- 4
cite$ -- 4
duplicate$ -- 305
empty$ -- 220
format.name$ -- 34
if$ -- 793
int.to.chr$ -- 2
int.to.str$ -- 1
missing$ -- 4
newline$ -- 87
num.names$ -- 28
pop$ -- 99
preamble$ -- 1
purify$ -- 54
quote$ -- 0
skip$ -- 120
stack$ -- 0
substring$ -- 254
swap$ -- 22
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 106
warning$ -- 0
while$ -- 27
width$ -- 0
write$ -- 124
