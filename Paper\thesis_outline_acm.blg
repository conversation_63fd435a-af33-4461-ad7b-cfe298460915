This is BibTeX, Version 0.99d (TeX Live 2025)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: thesis_outline_acm.aux
The style file: ACM-Reference-Format.bst
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated wiz_functions (elt_size=4) to 6000 items from 3000.
Database file #1: references.bib
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated glb_str_ptr (elt_size=4) to 20 items from 10.
Reallocated global_strs (elt_size=200001) to 20 items from 10.
Reallocated glb_str_end (elt_size=4) to 20 items from 10.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated wiz_functions (elt_size=4) to 9000 items from 6000.
Warning--empty publisher in bau2010state
Warning--empty address in bau2010state
Warning--empty publisher in chen2010automated
Warning--empty address in chen2010automated
Warning--empty publisher in fonseca2007testing
Warning--empty address in fonseca2007testing
Warning--empty publisher in mcallister2008leveraging
Warning--empty address in mcallister2008leveraging
Warning--page numbers missing in both pages and numpages fields in paquiao2025role
Warning--empty publisher in patil2016design
Warning--empty address in patil2016design
Warning--page numbers missing in both pages and numpages fields in suto2010analyzing
Warning--empty publisher in wang2019research
Warning--empty address in wang2019research
You've used 12 entries,
            6056 wiz_defined-function locations,
            1554 strings with 20421 characters,
and the built_in function-call counts, 14979 in all, are:
= -- 1984
> -- 446
< -- 0
+ -- 156
- -- 154
* -- 1012
:= -- 1377
add.period$ -- 60
call.type$ -- 12
change.case$ -- 90
chr.to.int$ -- 12
cite$ -- 26
duplicate$ -- 1344
empty$ -- 942
format.name$ -- 198
if$ -- 3755
int.to.chr$ -- 2
int.to.str$ -- 1
missing$ -- 54
newline$ -- 129
num.names$ -- 84
pop$ -- 545
preamble$ -- 1
purify$ -- 186
quote$ -- 0
skip$ -- 487
stack$ -- 0
substring$ -- 1147
swap$ -- 138
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 276
warning$ -- 14
while$ -- 82
width$ -- 0
write$ -- 265
(There were 14 warnings)
